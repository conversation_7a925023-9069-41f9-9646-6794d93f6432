version: '3.8'

services:
  # Zookeeper for <PERSON><PERSON><PERSON>
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log

  # Kafka Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    volumes:
      - kafka-data:/var/lib/kafka/data

  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:2.0.15
    hostname: mosquitto
    container_name: mosquitto
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./config/mosquitto:/mosquitto/config
      - mosquitto-data:/mosquitto/data
      - mosquitto-logs:/mosquitto/log

  # Kafka Connect
  kafka-connect:
    image: confluentinc/cp-kafka-connect:7.4.0
    hostname: kafka-connect
    container_name: kafka-connect
    depends_on:
      - kafka
      - mosquitto
    ports:
      - "8083:8083"
    environment:
      CONNECT_BOOTSTRAP_SERVERS: 'kafka:29092'
      CONNECT_REST_ADVERTISED_HOST_NAME: kafka-connect
      CONNECT_GROUP_ID: compose-connect-group
      CONNECT_CONFIG_STORAGE_TOPIC: docker-connect-configs
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_OFFSET_FLUSH_INTERVAL_MS: 10000
      CONNECT_OFFSET_STORAGE_TOPIC: docker-connect-offsets
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_STATUS_STORAGE_TOPIC: docker-connect-status
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_KEY_CONVERTER: org.apache.kafka.connect.storage.StringConverter
      CONNECT_VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
      CONNECT_PLUGIN_PATH: "/usr/share/java,/usr/share/confluent-hub-components"
      CONNECT_LOG4J_LOGGERS: org.apache.zookeeper=ERROR,org.I0Itec.zkclient=ERROR,org.reflections=ERROR
    volumes:
      - ./config/kafka-connect:/etc/kafka-connect
    command:
      - bash
      - -c
      - |
        confluent-hub install --no-prompt confluentinc/kafka-connect-mqtt:1.6.0
        /etc/confluent/docker/run

  # PostgreSQL for Druid metadata
  postgres:
    image: postgres:13
    hostname: postgres
    container_name: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_PASSWORD: FoolishPassword
      POSTGRES_USER: druid
      POSTGRES_DB: druid
    volumes:
      - postgres-data:/var/lib/postgresql/data

  # Druid Coordinator
  coordinator:
    image: apache/druid:0.24.1
    hostname: coordinator
    container_name: druid-coordinator
    depends_on:
      - zookeeper
      - postgres
    ports:
      - "8081:8081"
    environment:
      - DRUID_XMX=1g
      - DRUID_XMS=1g
      - druid_extensions_loadList=["druid-histogram", "druid-datasketches", "druid-lookups-cached-global", "postgresql-metadata-storage", "druid-kafka-indexing-service"]
      - druid_zk_service_host=zookeeper
      - druid_metadata_storage_host=postgres
      - druid_metadata_storage_type=postgresql
      - druid_metadata_storage_connector_connectURI=*************************************
      - druid_metadata_storage_connector_user=druid
      - druid_metadata_storage_connector_password=FoolishPassword
      - druid_coordinator_balancer_strategy=cachingCost
      - druid_indexer_runner_javaOptsArray=["-server", "-Xmx1g", "-Xms1g", "-XX:MaxDirectMemorySize=3g", "-Duser.timezone=UTC", "-Dfile.encoding=UTF-8", "-Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager"]
      - druid_indexer_fork_property_druid_processing_buffer_sizeBytes=268435456
    volumes:
      - druid-coordinator-var:/opt/druid/var
    command:
      - coordinator

  # Druid Broker
  broker:
    image: apache/druid:0.24.1
    hostname: broker
    container_name: druid-broker
    depends_on:
      - zookeeper
      - postgres
      - coordinator
    ports:
      - "8082:8082"
    environment:
      - DRUID_XMX=1g
      - DRUID_XMS=1g
      - druid_extensions_loadList=["druid-histogram", "druid-datasketches", "druid-lookups-cached-global", "postgresql-metadata-storage", "druid-kafka-indexing-service"]
      - druid_zk_service_host=zookeeper
      - druid_metadata_storage_host=postgres
      - druid_metadata_storage_type=postgresql
      - druid_metadata_storage_connector_connectURI=*************************************
      - druid_metadata_storage_connector_user=druid
      - druid_metadata_storage_connector_password=FoolishPassword
      - druid_broker_http_numConnections=5
      - druid_server_http_numThreads=40
      - druid_processing_buffer_sizeBytes=268435456
      - druid_processing_numMergeBuffers=2
      - druid_processing_numThreads=2
      - druid_broker_cache_useCache=true
      - druid_broker_cache_populateCache=true
    volumes:
      - druid-broker-var:/opt/druid/var
    command:
      - broker

  # Druid Historical
  historical:
    image: apache/druid:0.24.1
    hostname: historical
    container_name: druid-historical
    depends_on:
      - zookeeper
      - postgres
      - coordinator
    ports:
      - "8083:8083"
    environment:
      - DRUID_XMX=1g
      - DRUID_XMS=1g
      - druid_extensions_loadList=["druid-histogram", "druid-datasketches", "druid-lookups-cached-global", "postgresql-metadata-storage", "druid-kafka-indexing-service"]
      - druid_zk_service_host=zookeeper
      - druid_metadata_storage_host=postgres
      - druid_metadata_storage_type=postgresql
      - druid_metadata_storage_connector_connectURI=*************************************
      - druid_metadata_storage_connector_user=druid
      - druid_metadata_storage_connector_password=FoolishPassword
      - druid_server_http_numThreads=40
      - druid_processing_buffer_sizeBytes=268435456
      - druid_processing_numMergeBuffers=2
      - druid_processing_numThreads=2
      - druid_segmentCache_locations=[{"path":"/opt/druid/var/druid/segment-cache","maxSize":10737418240}]
      - druid_server_maxSize=10737418240
    volumes:
      - druid-historical-var:/opt/druid/var
    command:
      - historical

  # Druid MiddleManager
  middlemanager:
    image: apache/druid:0.24.1
    hostname: middlemanager
    container_name: druid-middlemanager
    depends_on:
      - zookeeper
      - postgres
      - coordinator
    ports:
      - "8091:8091"
      - "8100-8105:8100-8105"
    environment:
      - DRUID_XMX=1g
      - DRUID_XMS=1g
      - druid_extensions_loadList=["druid-histogram", "druid-datasketches", "druid-lookups-cached-global", "postgresql-metadata-storage", "druid-kafka-indexing-service"]
      - druid_zk_service_host=zookeeper
      - druid_metadata_storage_host=postgres
      - druid_metadata_storage_type=postgresql
      - druid_metadata_storage_connector_connectURI=*************************************
      - druid_metadata_storage_connector_user=druid
      - druid_metadata_storage_connector_password=FoolishPassword
      - druid_worker_capacity=2
      - druid_indexer_runner_javaOptsArray=["-server", "-Xmx1g", "-Xms1g", "-XX:MaxDirectMemorySize=3g", "-Duser.timezone=UTC", "-Dfile.encoding=UTF-8", "-Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager"]
      - druid_indexer_task_baseTaskDir=/opt/druid/var/druid/task
    volumes:
      - druid-middlemanager-var:/opt/druid/var
    command:
      - middlemanager

  # Druid Router
  router:
    image: apache/druid:0.24.1
    hostname: router
    container_name: druid-router
    depends_on:
      - zookeeper
      - postgres
      - coordinator
      - broker
    ports:
      - "8888:8888"
    environment:
      - DRUID_XMX=1g
      - DRUID_XMS=1g
      - druid_extensions_loadList=["druid-histogram", "druid-datasketches", "druid-lookups-cached-global", "postgresql-metadata-storage", "druid-kafka-indexing-service"]
      - druid_zk_service_host=zookeeper
      - druid_metadata_storage_host=postgres
      - druid_metadata_storage_type=postgresql
      - druid_metadata_storage_connector_connectURI=*************************************
      - druid_metadata_storage_connector_user=druid
      - druid_metadata_storage_connector_password=FoolishPassword
      - druid_router_http_numConnections=50
      - druid_server_http_numThreads=40
      - druid_router_http_readTimeout=PT5M
      - druid_router_http_requestHeaderSize=8192
      - druid_router_periodicTableDropTaskPeriod=PT300S
    volumes:
      - druid-router-var:/opt/druid/var
    command:
      - router

  # Redis for Superset
  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data

  # Superset
  superset:
    image: apache/superset:2.1.0
    hostname: superset
    container_name: superset
    depends_on:
      - postgres
      - redis
      - broker
    ports:
      - "8088:8088"
    environment:
      - SUPERSET_CONFIG_PATH=/app/pythonpath/superset_config.py
    volumes:
      - ./config/superset:/app/pythonpath
      - superset-home:/app/superset_home
    command: >
      bash -c "
        pip install pydruid &&
        superset fab create-admin --username admin --firstname Superset --lastname Admin --email <EMAIL> --password admin &&
        superset db upgrade &&
        superset init &&
        superset run -h 0.0.0.0 -p 8088 --with-threads --reload --debugger
      "

  # Python Data Publisher
  python-publisher:
    build:
      context: ./python-publisher
      dockerfile: Dockerfile
    hostname: python-publisher
    container_name: python-publisher
    depends_on:
      - mosquitto
    volumes:
      - ./data:/app/data
    environment:
      - MQTT_BROKER=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=twitter/tweets
    restart: unless-stopped

volumes:
  zookeeper-data:
  zookeeper-logs:
  kafka-data:
  mosquitto-data:
  mosquitto-logs:
  postgres-data:
  druid-coordinator-var:
  druid-broker-var:
  druid-historical-var:
  druid-middlemanager-var:
  druid-router-var:
  redis-data:
  superset-home:

networks:
  default:
    driver: bridge
