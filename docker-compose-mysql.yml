version: '3.8'

services:
  # Zookeeper for Ka<PERSON><PERSON>
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  # Kafka Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'

  # MQ<PERSON> Broker
  mosquitto:
    image: eclipse-mosquitto:2.0.15
    hostname: mosquitto
    container_name: mosquitto
    ports:
      - "1883:1883"
    volumes:
      - ./config/mosquitto:/mosquitto/config

  # Kafka Connect
  kafka-connect:
    image: confluentinc/cp-kafka-connect:7.4.0
    hostname: kafka-connect
    container_name: kafka-connect
    depends_on:
      - kafka
      - mosquitto
    ports:
      - "8083:8083"
    environment:
      CONNECT_BOOTSTRAP_SERVERS: 'kafka:29092'
      CONNECT_REST_ADVERTISED_HOST_NAME: kafka-connect
      CONNECT_GROUP_ID: compose-connect-group
      CONNECT_CONFIG_STORAGE_TOPIC: docker-connect-configs
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_OFFSET_STORAGE_TOPIC: docker-connect-offsets
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_STATUS_STORAGE_TOPIC: docker-connect-status
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_KEY_CONVERTER: org.apache.kafka.connect.storage.StringConverter
      CONNECT_VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
      CONNECT_PLUGIN_PATH: "/usr/share/java,/usr/share/confluent-hub-components"
    command:
      - bash
      - -c
      - |
        confluent-hub install --no-prompt confluentinc/kafka-connect-mqtt:1.6.0
        confluent-hub install --no-prompt confluentinc/kafka-connect-jdbc:10.7.0
        /etc/confluent/docker/run

  # MySQL Database
  mysql:
    image: mysql:8.0
    hostname: mysql
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: twitter_analytics
      MYSQL_USER: twitter_user
      MYSQL_PASSWORD: twitter_password
    volumes:
      - mysql-data:/var/lib/mysql
      - ./config/mysql:/docker-entrypoint-initdb.d

  # Redis for Superset
  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"

  # Superset
  superset:
    image: apache/superset:2.1.0
    hostname: superset
    container_name: superset
    depends_on:
      - mysql
      - redis
    ports:
      - "8088:8088"
    environment:
      - SUPERSET_CONFIG_PATH=/app/pythonpath/superset_config.py
    volumes:
      - ./config/superset-mysql:/app/pythonpath
    command: >
      bash -c "
        pip install mysqlclient &&
        superset fab create-admin --username admin --firstname Superset --lastname Admin --email <EMAIL> --password admin &&
        superset db upgrade &&
        superset init &&
        superset run -h 0.0.0.0 -p 8088 --with-threads --reload --debugger
      "

  # Python Data Publisher
  python-publisher:
    build:
      context: ./python-publisher
      dockerfile: Dockerfile
    hostname: python-publisher
    container_name: python-publisher
    depends_on:
      - mosquitto
    volumes:
      - ./data:/app/data
    environment:
      - MQTT_BROKER=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=twitter/tweets

  # MQTT to Kafka Bridge
  mqtt-kafka-bridge:
    build:
      context: ./mqtt-kafka-bridge
      dockerfile: Dockerfile
    hostname: mqtt-kafka-bridge
    container_name: mqtt-kafka-bridge
    depends_on:
      - mosquitto
      - kafka
    environment:
      - MQTT_BROKER=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=twitter/tweets
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_TOPIC=twitter-tweets

  # Kafka to MySQL Connector
  kafka-mysql-consumer:
    build:
      context: ./kafka-mysql-consumer
      dockerfile: Dockerfile
    hostname: kafka-mysql-consumer
    container_name: kafka-mysql-consumer
    depends_on:
      - kafka
      - mysql
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_TOPIC=twitter-tweets
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=twitter_analytics
      - MYSQL_USER=twitter_user
      - MYSQL_PASSWORD=twitter_password

volumes:
  mysql-data:

networks:
  default:
    driver: bridge
