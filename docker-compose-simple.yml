services:
  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:2.0.15
    hostname: mosquitto
    container_name: mosquitto-simple
    ports:
      - "1884:1883"
    volumes:
      - ./config/mosquitto:/mosquitto/config

  # Python Data Publisher
  python-publisher:
    build:
      context: ./python-publisher
      dockerfile: Dockerfile
    hostname: python-publisher
    container_name: python-publisher-simple
    depends_on:
      - mosquitto
    volumes:
      - ./data:/app/data
    environment:
      - MQTT_BROKER=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=twitter/tweets

networks:
  default:
    driver: bridge
