# Twitter Personality Analysis Real-Time Pipeline

This project implements a real-time data pipeline for Twitter personality analysis using Docker containers. The pipeline processes Twitter data and personality profiles through <PERSON><PERSON><PERSON>, Kafka, Druid, and displays insights in a Superset dashboard.

## Architecture

```
Python Script → MQTT Broker → Kafka → Druid → Superset Dashboard
```

### Components

1. **Python Publisher** (python:3.9-slim): Reads Twitter data every 2 seconds and publishes to MQTT
2. **MQTT Broker** (eclipse-mosquitto:2.0.15): Message broker for real-time data streaming
3. **Kafka Cluster**: 
   - Zookeeper (confluentinc/cp-zookeeper:7.4.0)
   - Kafka Broker (confluentinc/cp-kafka:7.4.0)
   - Kafka Connect (confluentinc/cp-kafka-connect:7.4.0) with MQTT Source Connector
4. **Druid** (apache/druid:0.24.1): Real-time analytics database
5. **Superset** (apache/superset:2.1.0): Business intelligence dashboard
6. **PostgreSQL** (postgres:13): Metadata storage for Druid
7. **Redis** (redis:7-alpine): Caching for Superset

## Data Sources

- `tweets1.json`: User tweets data
- `users1.json`: User profile information
- `edges1.json`: User connection data
- `mbti_labels.csv`: MBTI personality type labels

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 8GB RAM available for containers
- Ports 1883, 5432, 6379, 8081-8083, 8088, 8091, 8888, 9001, 9092, 9101 available

### Setup

1. **Clone and navigate to the project directory**
   ```bash
   cd umair_twitter_app_v1
   ```

2. **Run the complete setup**
   ```bash
   chmod +x setup-pipeline.sh
   ./setup-pipeline.sh
   ```

   This script will:
   - Start all Docker services
   - Configure Kafka Connect with MQTT Source Connector
   - Set up Druid ingestion from Kafka
   - Configure Superset with Druid connection

3. **Access the services**
   - Superset Dashboard: http://localhost:8088 (admin/admin)
   - Druid Console: http://localhost:8888
   - Kafka Connect: http://localhost:8083

## Manual Setup (Alternative)

If the automated setup fails, you can set up components manually:

### 1. Start Services
```bash
docker-compose up -d
```

### 2. Configure Kafka Connect
```bash
# Wait for Kafka Connect to be ready
curl http://localhost:8083/connectors

# Create Kafka topic
docker exec kafka kafka-topics --create --topic twitter-tweets --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1

# Deploy MQTT connector
curl -X POST http://localhost:8083/connectors \
  -H 'Content-Type: application/json' \
  -d @config/kafka-connect/mqtt-source-connector.json
```

### 3. Configure Druid Ingestion
```bash
# Submit ingestion spec
curl -X POST http://localhost:8888/druid/indexer/v1/supervisor \
  -H 'Content-Type: application/json' \
  -d @config/druid/twitter-ingestion-spec.json
```

### 4. Configure Superset
```bash
# Access Superset at http://localhost:8088
# Login: admin/admin
# Add Druid database: druid://broker:8082/druid/v2/sql/
```

## Monitoring

### Check Pipeline Status

1. **Python Publisher Logs**
   ```bash
   docker logs python-publisher
   ```

2. **MQTT Messages**
   ```bash
   docker exec mosquitto mosquitto_sub -h localhost -t "twitter/tweets"
   ```

3. **Kafka Messages**
   ```bash
   docker exec kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic twitter-tweets --from-beginning
   ```

4. **Kafka Connect Status**
   ```bash
   curl http://localhost:8083/connectors/mqtt-source-connector/status
   ```

5. **Druid Ingestion Status**
   ```bash
   curl http://localhost:8888/druid/indexer/v1/supervisor
   ```

## Troubleshooting

### Common Issues

1. **Superset "Load data" disabled**
   - Ensure Druid connection is properly configured
   - Check that pydruid is installed in Superset container
   - Verify Druid broker is accessible at broker:8082

2. **Kafka Connect fails to start**
   - Check if MQTT broker is running
   - Verify Kafka is accessible
   - Check connector logs: `docker logs kafka-connect`

3. **Druid ingestion not working**
   - Verify Kafka topic exists and has messages
   - Check Druid logs: `docker logs druid-middlemanager`
   - Ensure PostgreSQL metadata store is accessible

4. **Memory issues**
   - Increase Docker memory allocation to at least 8GB
   - Reduce JVM heap sizes in docker-compose.yml if needed

### Logs

View logs for specific services:
```bash
docker logs <service-name>
```

Service names: `python-publisher`, `mosquitto`, `kafka`, `kafka-connect`, `druid-coordinator`, `druid-broker`, `druid-historical`, `druid-middlemanager`, `superset`

## Data Schema

The pipeline processes Twitter data with the following schema:

```json
{
  "user_id": "integer",
  "screen_name": "string",
  "tweet": "string",
  "timestamp": "string",
  "iso_timestamp": "string",
  "location": "string",
  "verified": "boolean",
  "statuses_count": "integer",
  "mbti_personality": "string",
  "total_retweet_count": "integer",
  "total_favorite_count": "integer"
}
```

## Stopping the Pipeline

```bash
docker-compose down
```

To remove all data:
```bash
docker-compose down -v
```

## Alternative Solution: MySQL-Based Pipeline

If you encounter Druid compatibility issues, use the MySQL-based alternative:

### MySQL Pipeline Setup

1. **Use the MySQL Docker Compose file**
   ```bash
   docker-compose -f docker-compose-mysql.yml up -d
   ```

2. **This alternative provides:**
   - MySQL instead of Druid for data storage
   - Direct Kafka-to-MySQL consumer
   - Better Superset compatibility
   - Simpler configuration

3. **Access services:**
   - Superset: http://localhost:8088 (admin/admin)
   - MySQL: localhost:3306 (twitter_user/twitter_password)

### MySQL Pipeline Benefits
- ✅ Guaranteed Superset compatibility
- ✅ Simpler setup and maintenance
- ✅ Better performance for smaller datasets
- ✅ Familiar SQL interface
- ✅ Pre-built analytics views

## Testing and Validation

Run the comprehensive test script to validate your pipeline:

```powershell
# For Windows PowerShell
.\test-pipeline.ps1
```

```bash
# For Linux/Mac (convert to bash if needed)
chmod +x test-pipeline.sh && ./test-pipeline.sh
```

The test script validates:
- Docker container health
- Service connectivity
- MQTT message flow
- Kafka message processing
- Kafka Connect status
- Druid/MySQL data ingestion
- End-to-end data pipeline

## Troubleshooting Guide

### Common Issues and Solutions

1. **Superset "Load data" disabled**
   ```bash
   # Check Druid connection
   docker logs superset
   # Ensure pydruid is installed
   docker exec superset pip install pydruid
   ```

2. **Kafka Connect MQTT connector fails**
   ```bash
   # Check connector status
   curl http://localhost:8083/connectors/mqtt-source-connector/status
   # Restart if needed
   curl -X POST http://localhost:8083/connectors/mqtt-source-connector/restart
   ```

3. **Druid ingestion not working**
   ```bash
   # Check supervisor status
   curl http://localhost:8888/druid/indexer/v1/supervisor
   # Check middlemanager logs
   docker logs druid-middlemanager
   ```

4. **Memory issues**
   - Increase Docker Desktop memory to 8GB+
   - Reduce JVM heap sizes in docker-compose.yml
   - Use MySQL alternative for lower resource usage

5. **Port conflicts**
   ```bash
   # Check which ports are in use
   netstat -an | findstr "8088\|8888\|9092\|1883"
   # Stop conflicting services or change ports in docker-compose.yml
   ```

### Monitoring Commands

```bash
# Check all container status
docker-compose ps

# View real-time logs
docker-compose logs -f python-publisher

# Test MQTT messages
docker exec mosquitto mosquitto_sub -h localhost -t "twitter/tweets"

# Test Kafka messages
docker exec kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic twitter-tweets --from-beginning

# Check Druid data
curl -X POST http://localhost:8082/druid/v2/ \
  -H 'Content-Type: application/json' \
  -d '{"queryType":"timeseries","dataSource":"twitter-tweets","granularity":"minute","aggregations":[{"type":"count","name":"count"}],"intervals":["2020-01-01/2030-01-01"]}'
```

## Performance Optimization

### For Production Use

1. **Increase resource allocation**
   ```yaml
   # In docker-compose.yml, add resource limits
   deploy:
     resources:
       limits:
         memory: 2G
       reservations:
         memory: 1G
   ```

2. **Optimize Kafka settings**
   ```yaml
   environment:
     KAFKA_NUM_PARTITIONS: 6
     KAFKA_DEFAULT_REPLICATION_FACTOR: 3
   ```

3. **Tune Druid for your data volume**
   ```yaml
   environment:
     - DRUID_XMX=4g
     - DRUID_XMS=4g
   ```

## Data Schema and Analytics

### Available Data Fields
- `user_id`: Twitter user ID
- `screen_name`: Twitter handle
- `tweet`: Tweet content
- `timestamp`: Tweet timestamp
- `location`: User location
- `verified`: Verification status
- `mbti_personality`: MBTI personality type
- `statuses_count`: Total user tweets
- `total_retweet_count`: User's total retweets
- `total_favorite_count`: User's total favorites

### Pre-built Analytics (MySQL version)
- `user_tweet_stats`: User-level aggregations
- `mbti_analytics`: Personality type insights
- `hourly_tweet_volume`: Time-based analytics

## Support

For issues or questions:
1. Run the test script to identify problems
2. Check service logs: `docker logs <service-name>`
3. Ensure all prerequisites are met
4. Consider the MySQL alternative for simpler setup

The pipeline requires all components to be healthy for proper operation. Use the provided monitoring and testing tools to maintain system health.
