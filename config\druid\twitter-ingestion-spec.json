{"type": "kafka", "spec": {"dataSchema": {"dataSource": "twitter-tweets", "timestampSpec": {"column": "iso_timestamp", "format": "iso", "missingValue": null}, "dimensionsSpec": {"dimensions": [{"type": "string", "name": "user_id", "multiValueHandling": "SORTED_ARRAY", "createBitmapIndex": true}, {"type": "string", "name": "screen_name", "multiValueHandling": "SORTED_ARRAY", "createBitmapIndex": true}, {"type": "string", "name": "tweet", "multiValueHandling": "SORTED_ARRAY", "createBitmapIndex": false}, {"type": "string", "name": "location", "multiValueHandling": "SORTED_ARRAY", "createBitmapIndex": true}, {"type": "string", "name": "mbti_personality", "multiValueHandling": "SORTED_ARRAY", "createBitmapIndex": true}, {"type": "boolean", "name": "verified"}]}, "metricsSpec": [{"type": "count", "name": "tweet_count"}, {"type": "longSum", "name": "statuses_count", "fieldName": "statuses_count"}, {"type": "longSum", "name": "total_retweet_count", "fieldName": "total_retweet_count"}, {"type": "longSum", "name": "total_favorite_count", "fieldName": "total_favorite_count"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "MINUTE", "rollup": false, "intervals": null}}, "ioConfig": {"topic": "twitter-tweets", "inputFormat": {"type": "json"}, "replicas": 1, "taskCount": 1, "taskDuration": "PT1H", "consumerProperties": {"bootstrap.servers": "kafka:29092", "group.id": "druid-twitter-consumer", "auto.offset.reset": "earliest"}, "pollTimeout": 100, "startDelay": "PT5S", "period": "PT30S", "useEarliestOffset": true, "completionTimeout": "PT30M", "lateMessageRejectionPeriod": null, "earlyMessageRejectionPeriod": null, "lateMessageRejectionStartDateTime": null, "configOverrides": null, "idleConfig": null, "stream": "twitter-tweets", "useEarliestSequenceNumber": true}, "tuningConfig": {"type": "kafka", "appendableIndexSpec": {"type": "onheap", "preserveExistingMetrics": false}, "maxRowsInMemory": 150000, "maxBytesInMemory": 0, "skipBytesInMemoryOverheadCheck": false, "maxRowsPerSegment": 5000000, "maxTotalRows": null, "intermediatePersistPeriod": "PT10M", "maxPendingPersists": 0, "indexSpec": {"bitmap": {"type": "roaring"}, "dimensionCompression": "lz4", "metricCompression": "lz4", "longEncoding": "longs"}, "indexSpecForIntermediatePersists": {"bitmap": {"type": "roaring"}, "dimensionCompression": "lz4", "metricCompression": "lz4", "longEncoding": "longs"}, "reportParseExceptions": false, "handoffConditionTimeout": 0, "resetOffsetAutomatically": false, "segmentWriteOutMediumFactory": null, "workerThreads": null, "chatThreads": null, "chatRetries": 8, "httpTimeout": "PT10S", "shutdownTimeout": "PT80S", "offsetFetchPeriod": "PT30S", "intermediateHandoffPeriod": "*********47D", "logParseExceptions": false, "maxParseExceptions": 2147483647, "maxSavedParseExceptions": 0}}, "context": null, "suspended": false}